<script>
  export let size = 80;
  export let color = '#4ade80'; // Green-400 in Tailwind
  export let strokeWidth = 3;
  export let duration = 800; // Animation duration in ms
</script>

<div class="success-checkmark" style="width: {size}px; height: {size}px;">
  <svg
    viewBox="0 0 52 52"
    width={size}
    height={size}
  >
    <circle
      class="checkmark-circle"
      cx="26"
      cy="26"
      r="24"
      fill="none"
      stroke={color}
      stroke-width={strokeWidth}
      style="--duration: {duration}ms"
    />
    <path
      class="checkmark-check"
      fill="none"
      stroke={color}
      stroke-width={strokeWidth}
      d="M14.1 27.2l7.1 7.2 16.7-16.8"
      style="--duration: {duration}ms"
    />
  </svg>
</div>

<style>
  .success-checkmark {
    display: inline-flex;
    justify-content: center;
    align-items: center;
  }

  .checkmark-circle {
    stroke-dasharray: 166;
    stroke-dashoffset: 166;
    animation: stroke var(--duration) cubic-bezier(0.65, 0, 0.45, 1) forwards;
  }

  .checkmark-check {
    stroke-dasharray: 48;
    stroke-dashoffset: 48;
    animation: stroke var(--duration) cubic-bezier(0.65, 0, 0.45, 1) forwards;
    animation-delay: calc(var(--duration) * 0.3);
  }

  @keyframes stroke {
    100% {
      stroke-dashoffset: 0;
    }
  }
</style>
