import * as brevo from '@getbrevo/brevo';
import { BREVO_API_KEY } from '$lib/server/env';

/**
 * Initialize the Brevo API client with the API key
 * @returns {brevo.ContactsApi} Initialized Brevo contacts API instance
 */
export function initBrevoClient() {
  const apiInstance = new brevo.ContactsApi();
  const apiKey = apiInstance.authentications['apiKey'];
  apiKey.apiKey = BREVO_API_KEY;
  return apiInstance;
}

/**
 * Add a contact to a specific list in Brevo
 * @param {string} email - Email address to add
 * @param {number} listId - Brevo list ID to add the contact to
 * @returns {Promise<Object>} Result of the operation
 */
export async function addContactToList(email, listId) {
  const apiInstance = initBrevoClient();
  const contactEmails = new brevo.AddContactToList();
  contactEmails.emails = [email];

  try {
    const result = await apiInstance.addContactToList(listId, contactEmails);
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error };
  }
}

/**
 * Create a new contact in Brevo
 * @param {string} email - Email address of the contact
 * @param {Object} attributes - Additional attributes for the contact
 * @param {Array<number>} listIds - List IDs to add the contact to
 * @returns {Promise<Object>} Result of the operation
 */
export async function createContact(email, attributes = {}, listIds = []) {
  const apiInstance = initBrevoClient();

  if (!apiInstance.authentications['apiKey'].apiKey) {
    return { success: false, error: { message: 'API key configuration error' } };
  }

  const createContact = new brevo.CreateContact();
  createContact.email = email;
  createContact.attributes = attributes;

  if (listIds.length > 0) {
    createContact.listIds = listIds;
  }

  try {
    const result = await apiInstance.createContact(createContact);
    return { success: true, data: result };
  } catch (error) {
    // Handle authentication errors
    if (error.statusCode === 401 || (error.response && error.response.statusCode === 401)) {
      return {
        success: false,
        error: {
          message: 'Authentication error with email service',
          statusCode: 401
        }
      };
    }

    // Handle duplicate contact errors (this is actually not an error for our use case)
    if (
      (error.statusCode === 400 || (error.response && error.response.statusCode === 400)) &&
      ((error.body && error.body.code === 'duplicate_parameter') ||
       (error.response && error.response.body && error.response.body.code === 'duplicate_parameter') ||
       (error.message && error.message.includes('already exists')))
    ) {
      return {
        success: true,
        data: { message: 'Contact already exists' },
        alreadyExists: true
      };
    }

    return {
      success: false,
      error: {
        message: error.message || 'Error creating contact',
        statusCode: error.statusCode || (error.response && error.response.statusCode) || 500
      }
    };
  }
}

/**
 * Check if a contact exists in Brevo
 * @param {string} email - Email address to check
 * @returns {Promise<Object>} Result of the operation
 */
export async function checkContactExists(email) {
  const apiInstance = initBrevoClient();

  if (!apiInstance.authentications['apiKey'].apiKey) {
    return { error: true, message: 'API key configuration error' };
  }

  try {
    // First approach: Try to get contact info directly
    try {
      const result = await apiInstance.getContactInfo(email);
      return { exists: true, data: result };
    } catch (directError) {
      // If it's a 404, the contact doesn't exist (this is expected)
      if (directError.statusCode === 404 || (directError.response && directError.response.statusCode === 404)) {
        return { exists: false };
      }

      // For other errors, try the alternative approach
      throw directError;
    }
  } catch (error) {
    // Alternative approach: Use getContacts with a filter
    try {
      const filters = {
        limit: 1,
        offset: 0
      };

      const contacts = await apiInstance.getContacts(filters);

      // Manually check if the email exists in the returned contacts
      const contactExists = contacts && contacts.contacts &&
        contacts.contacts.some(contact => contact.email === email);

      if (contactExists) {
        return { exists: true };
      } else {
        return { exists: false };
      }
    } catch (alternativeError) {
      // Handle authentication errors
      if (alternativeError.statusCode === 401 || (alternativeError.response && alternativeError.response.statusCode === 401)) {
        return {
          error: true,
          message: 'Authentication error with email service',
          statusCode: 401
        };
      }

      return {
        error: true,
        message: 'Error checking contact status',
        statusCode: alternativeError.statusCode || (alternativeError.response && alternativeError.response.statusCode) || 500
      };
    }
  }
}
