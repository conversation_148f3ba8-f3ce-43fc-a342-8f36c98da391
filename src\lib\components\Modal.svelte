<script>
  import { fade, scale } from 'svelte/transition';
  import { quintOut } from 'svelte/easing';
  import { createEventDispatcher, onDestroy } from 'svelte';

  export let show = false;
  export let title = '';
  export let closeOnClickOutside = true;

  const dispatch = createEventDispatcher();

  function handleKeydown(e) {
    if (e.key === 'Escape' && show) {
      close();
    }
  }

  function handleClickOutside(e) {
    if (closeOnClickOutside && e.target === e.currentTarget) {
      close();
    }
  }

  function close() {
    show = false;
    dispatch('close');
  }

  // Handle body overflow
  let bodyScrollLocked = false;

  function lockBodyScroll() {
    if (!bodyScrollLocked) {
      document.body.classList.add('overflow-hidden');
      bodyScrollLocked = true;
    }
  }

  function unlockBodyScroll() {
    if (bodyScrollLocked) {
      document.body.classList.remove('overflow-hidden');
      bodyScrollLocked = false;
    }
  }

  $: if (show) {
    lockBodyScroll();
  } else {
    unlockBodyScroll();
  }

  onDestroy(() => {
    unlockBodyScroll();
  });
</script>

<svelte:window on:keydown={handleKeydown} />

{#if show}
  <div
    class="fixed inset-0 z-50 flex items-center justify-center p-3 sm:p-4 bg-gray-900/70 backdrop-blur-sm"
    transition:fade={{ duration: 200 }}
    on:click={handleClickOutside}
    on:keydown={handleKeydown}
    role="dialog"
    aria-modal="true"
    aria-labelledby={title ? 'modal-title' : undefined}
    tabindex="-1"
  >
    <div
      class="relative w-full max-w-[90%] sm:max-w-md bg-gray-800/90 backdrop-blur-md rounded-xl shadow-2xl border border-gray-700/50 overflow-hidden"
      transition:scale={{ duration: 250, easing: quintOut, start: 0.95 }}
    >
      <!-- Blurred gradient accent -->
      <div class="absolute -top-20 -left-20 w-32 sm:w-40 h-32 sm:h-40 bg-sky-500 rounded-full filter blur-3xl opacity-20"></div>
      <div class="absolute -bottom-20 -right-20 w-32 sm:w-40 h-32 sm:h-40 bg-purple-600 rounded-full filter blur-3xl opacity-20"></div>

      <div class="relative z-10 p-4 sm:p-6">
        {#if title}
          <h3 id="modal-title" class="text-lg sm:text-xl font-semibold text-slate-100 mb-3 sm:mb-4">{title}</h3>
        {/if}

        <div class="modal-content text-sm sm:text-base">
          <slot></slot>
        </div>

        <div class="mt-4 sm:mt-6 flex justify-end gap-2 sm:gap-3">
          <slot name="actions">
            <button
              class="px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg bg-gray-700 hover:bg-gray-600 text-slate-200 transition-colors text-sm sm:text-base"
              on:click={close}
            >
              Close
            </button>
          </slot>
        </div>
      </div>
    </div>
  </div>
{/if}
