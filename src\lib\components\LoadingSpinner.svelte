<script>
  export let size = 24;
  export let color = '#38bdf8'; // sky-400 in Tailwind
  export let thickness = 3;
</script>

<div 
  class="loading-spinner" 
  style="width: {size}px; height: {size}px; border-width: {thickness}px; border-color: {color};"
>
</div>

<style>
  .loading-spinner {
    display: inline-block;
    border-style: solid;
    border-radius: 50%;
    border-top-color: transparent !important;
    border-right-color: transparent !important;
    animation: spin 0.8s linear infinite;
  }
  
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
</style>
