# Brevo Email List Integration for Anithing.moe

This document explains how to set up and configure the Brevo email list integration for the Anithing.moe website.

## Prerequisites

1. A Brevo account (sign up at [https://www.brevo.com/](https://www.brevo.com/))
2. Node.js and npm installed

## Setup Steps

### 1. Get Your Brevo API Key

1. Log in to your Brevo account
2. Go to **Settings** > **API Keys & Webhooks**
3. Create a new API key or use an existing one
4. Copy the API key

### 2. Configure Environment Variables

1. Create a `.env` file in the root of your project (if it doesn't exist already)
2. Add your Brevo API key:
   ```
   BREVO_API_KEY=your_brevo_api_key_here
   ```

### 3. Verify Your API Key

Run the verification script to ensure your API key is working correctly:

```bash
npm run verify-brevo
```

If successful, you'll see account information displayed. If not, check your API key and try again.

### 4. Create a Contact List in Brevo

1. In your Brevo dashboard, go to **Contacts** > **Lists**
2. Click **Create a list**
3. Name your list (e.g., "Anithing.moe Newsletter")
4. Note the List ID (you'll need this for configuration)

### 5. Update the Configuration

1. Open `src/lib/config/brevo.js`
2. Update the `defaultListId` with your actual list ID from Brevo:
   ```javascript
   defaultListId: 123, // Replace with your actual list ID
   ```

## Testing the Integration

1. Start your development server:
   ```
   npm run dev
   ```
2. Go to your website and test the email signup form
3. Check your Brevo dashboard to verify that contacts are being added to your list

## Troubleshooting

If you encounter issues:

### Authentication Errors (401 Unauthorized)

1. Verify your API key is correct in the `.env` file
2. Run the verification script: `npm run verify-brevo`
3. Check that the API key is being loaded correctly by SvelteKit
4. Make sure you're not hitting API rate limits

### Contact List Issues

1. Verify the list ID in `src/lib/config/brevo.js`
2. Check if the list exists in your Brevo dashboard
3. Ensure you have permission to add contacts to this list

### Server-Side Errors

1. Check the server logs for detailed error messages
2. Verify that environment variables are loading correctly
3. Make sure the Brevo SDK is properly installed

### Client-Side Errors

1. Check the browser console for errors
2. Verify that the API endpoint is being called correctly
3. Check network requests for any error responses

## Additional Configuration

### Custom Fields

If you want to add custom fields to your contacts, you can modify the `defaultAttributes` in `src/lib/config/brevo.js`:

```javascript
defaultAttributes: {
  WEBSITE: 'anithing.moe',
  SOURCE: 'website_signup',
  CUSTOM_FIELD: 'custom_value'
}
```

Note: Custom fields must be created in your Brevo account first.

### Multiple Lists

If you want to add contacts to multiple lists, modify the `listIds` array in `src/routes/api/subscribe/+server.js`:

```javascript
const listIds = [brevoConfig.defaultListId, 456, 789]; // Add additional list IDs
```

### Double Opt-In

To implement double opt-in (recommended for GDPR compliance):

1. In your Brevo account, go to **Contacts** > **Settings** > **Double Opt-In**
2. Create a DOI process with a confirmation email template
3. Modify the API call to use the DOI endpoint instead of direct subscription

## Resources

- [Brevo API Documentation](https://developers.brevo.com/)
- [Brevo Node.js SDK Documentation](https://github.com/getbrevo/brevo-node)
- [SvelteKit Environment Variables](https://kit.svelte.dev/docs/modules#$env-dynamic-private)
