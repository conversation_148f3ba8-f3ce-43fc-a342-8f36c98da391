import { env } from '$env/dynamic/private';

/**
 * Server-side environment variables
 */
export const BREVO_API_KEY = env.BREVO_API_KEY;

/**
 * Validate required environment variables
 */
export function validateEnv() {
  const requiredVars = [
    { name: 'BREVO_API_KEY', value: BREVO_API_KEY }
  ];

  const missingVars = requiredVars
    .filter(v => !v.value)
    .map(v => v.name);

  if (missingVars.length > 0) {
    return false;
  }

  return true;
}
