/* src/app.postcss */

/* 1. Import Tailwind CSS v4 */
@import "tailwindcss";

/* 2. Define any custom theme properties (CSS variables) if needed */
/*    Most theme extensions are now through CSS variables that Tailwind generates
      or by defining custom utilities. */
@theme {
  /* Example: Define a custom color variable (Tailwind v4 uses oklch for its palette) */
  /* --color-brand-primary: oklch(0.62 0.22 255); */

  /* Example: Define custom spacing unit if needed for dynamic utilities */
  /* --spacing: 0.25rem; (Tailwind v4 does this by default) */

  /* Custom breakpoint for extra small screens */
  --breakpoint-xs: 480px;
}

/* 3. Define global base styles (if not covered by Tailwind's preflight or your needs) */
@layer base {
  body {
    @apply bg-gray-900 text-slate-100; /* Base dark theme */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden; /* Prevent horizontal scroll from large blurred elements */
  }
}

/* 4. Define custom component classes (if any) */
@layer components {
  /* .custom-button {
    @apply px-4 py-2 bg-blue-500 text-white rounded;
  } */
}

/* 5. Define custom utilities */
@layer utilities {
  .animation-delay-2000 {
    animation-delay: 2s;
  }
  .animation-delay-4000 {
    animation-delay: 4s;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .animate-fadeIn {
    animation: fadeIn 0.3s ease-out forwards;
  }
}