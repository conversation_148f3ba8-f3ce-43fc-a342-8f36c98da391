import { json } from '@sveltejs/kit';
import { checkContactExists, createContact, addContactToList } from '$lib/services/brevo';
import { brevoConfig } from '$lib/config/brevo';
import { validateEnv } from '$lib/server/env';

/**
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function POST({ request }) {
  try {
    // Validate environment variables
    if (!validateEnv()) {
      return json({
        success: false,
        message: 'Server configuration error. Please contact the administrator.'
      }, { status: 500 });
    }

    const { email } = await request.json();

    // Validate email
    if (!email || !/\S+@\S+\.\S+/.test(email)) {
      return json({ success: false, message: 'Please provide a valid email address.' }, { status: 400 });
    }

    // Check if the contact already exists
    const contactCheck = await checkContactExists(email);

    if (!contactCheck.error && contactCheck.exists) {
      return json({
        success: true,
        message: 'You are already subscribed to our newsletter!'
      });
    }

    // Create a new contact (or update if it already exists)
    const attributes = brevoConfig.defaultAttributes;
    const listIds = [brevoConfig.defaultListId];
    const result = await createContact(email, attributes, listIds);

    // If the contact creation was successful or the contact already exists
    if (result.success || result.alreadyExists) {
      // If the contact already exists but we need to add it to a list
      if (result.alreadyExists) {
        try {
          await addContactToList(email, brevoConfig.defaultListId);
        } catch (listError) {
          // Continue anyway, as the contact exists
        }
      }

      return json({
        success: true,
        message: 'Thanks for subscribing! See you soon!'
      });
    }

    // If we get here, there was an error creating the contact
    return json({
      success: false,
      message: 'An error occurred while subscribing. Please try again later.'
    }, { status: 500 });
  } catch (error) {
    return json({
      success: false,
      message: 'An error occurred while subscribing. Please try again later.'
    }, { status: 500 });
  }
}
