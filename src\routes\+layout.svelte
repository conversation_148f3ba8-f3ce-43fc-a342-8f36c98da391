<script>
	import "../app.css"; // Import global styles, including Tailwind
  </script>

  <div class="relative min-h-screen overflow-hidden"> <!-- bg-gray-900 text-slate-100 are now in app.postcss @layer base -->
	<!-- Blurred Gradient Blobs -->
	<div
	  class="absolute top-[-10%] left-[-10%] w-[250px] h-[250px] xs:w-[300px] xs:h-[300px] sm:w-[600px] sm:h-[600px] bg-purple-600 rounded-full filter blur-3xl opacity-30 animate-pulse"
	></div>
	<div
	  class="absolute bottom-[-10%] right-[-10%] w-[200px] h-[200px] xs:w-[250px] xs:h-[250px] sm:w-[500px] sm:h-[500px] bg-sky-500 rounded-full filter blur-3xl opacity-30 animate-pulse animation-delay-2000"
	></div>
	 <div
	  class="absolute top-[20%] right-[5%] w-[150px] h-[150px] xs:w-[180px] xs:h-[180px] sm:w-[300px] sm:h-[300px] bg-pink-500 rounded-full filter blur-2xl opacity-20 animate-pulse animation-delay-4000"
	></div>

	<div class="relative z-10">
	  <slot />
	</div>
  </div>
